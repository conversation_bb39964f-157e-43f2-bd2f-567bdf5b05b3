name: PathForge AI - CD Pipeline

on:
  push:
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      create_tag:
        description: 'Create new tag and build images'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: dockerhub.csharpp.com
  IMAGE_NAME: pathforge-ai

jobs:
  auto-version:
    runs-on: ubuntu-latest
    if: github.event.inputs.create_tag == 'true'
    permissions:
      contents: write
    outputs:
      new_version: ${{ steps.bump.outputs.NEW_VERSION }}
      new_tag: ${{ steps.bump.outputs.NEW_TAG }}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

    - name: Bump version and create tag
      id: bump
      run: |
        chmod +x scripts/version-bump.sh
        ./scripts/version-bump.sh

    - name: Push tag
      run: |
        git push origin ${{ steps.bump.outputs.NEW_TAG }}

  get-version:
    runs-on: ubuntu-latest
    needs: [auto-version]
    if: always() && (startsWith(github.ref, 'refs/tags/v') || needs.auto-version.result == 'success')
    outputs:
      version: ${{ steps.version.outputs.VERSION }}
      tag: ${{ steps.version.outputs.TAG }}
    steps:
    - name: Set version variables
      id: version
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ] && [ "${{ github.event.inputs.create_tag }}" = "true" ]; then
          VERSION="${{ needs.auto-version.outputs.new_version }}"
          TAG="${{ needs.auto-version.outputs.new_tag }}"
        else
          TAG="${{ github.ref_name }}"
          VERSION="${TAG#v}"
        fi
        echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
        echo "TAG=$TAG" >> $GITHUB_OUTPUT

  build-and-push:
    needs: [auto-version, get-version]
    if: always() && (startsWith(github.ref, 'refs/tags/v') || needs.auto-version.result == 'success')
    uses: ./.github/workflows/reusable-docker-build.yml
    permissions:
      contents: read
      packages: write
    with:
      push: true
      registry: dockerhub.csharpp.com
      tag-prefix: ${{ needs.get-version.outputs.version }}
    secrets:
      DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}

  notify-portainer:
    runs-on: ubuntu-latest
    needs: [get-version, build-and-push]
    if: always() && needs.build-and-push.result == 'success'
    permissions:
      contents: read

    steps:
    - name: Trigger Portainer webhook
      run: |
        if [ -n "${{ secrets.PORTAINER_WEBHOOK_URL }}" ]; then
          echo "Triggering Portainer webhook for deployment update..."
          curl -X POST "${{ secrets.PORTAINER_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{
              "service": "pathforge-ai",
              "version": "${{ needs.get-version.outputs.version }}",
              "registry": "${{ env.REGISTRY }}",
              "images": {
                "agent_service": "${{ env.REGISTRY }}/pathforge-ai/agent_service:${{ needs.get-version.outputs.version }}",
                "streamlit_app": "${{ env.REGISTRY }}/pathforge-ai/streamlit_app:${{ needs.get-version.outputs.version }}"
              },
              "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
            }' || echo "Failed to trigger Portainer webhook"
        else
          echo "PORTAINER_WEBHOOK_URL secret not configured, skipping webhook"
        fi

  security-scan:
    runs-on: ubuntu-latest
    needs: [get-version, build-and-push]
    if: always() && needs.build-and-push.result == 'success'
    permissions:
      contents: read
      security-events: write
      actions: read  # Required for workflow run access

    strategy:
      matrix:
        service: [agent_service, streamlit_app]

    steps:
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/pathforge-ai/${{ matrix.service }}:${{ needs.get-version.outputs.version }}
        format: 'sarif'
        output: 'trivy-results-${{ matrix.service }}.sarif'

    - name: Validate SARIF file
      run: |
        if [ ! -f "trivy-results-${{ matrix.service }}.sarif" ]; then
          echo "Error: SARIF file not found!"
          exit 1
        fi
        echo "SARIF file created successfully for ${{ matrix.service }}"
        echo "File size: $(stat -c%s trivy-results-${{ matrix.service }}.sarif) bytes"

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      id: upload-sarif
      continue-on-error: true
      with:
        sarif_file: 'trivy-results-${{ matrix.service }}.sarif'

    - name: Upload SARIF as artifact (fallback)
      if: steps.upload-sarif.outcome == 'failure'
      uses: actions/upload-artifact@v4
      with:
        name: trivy-sarif-${{ matrix.service }}
        path: 'trivy-results-${{ matrix.service }}.sarif'
        retention-days: 30

    - name: Security scan summary
      run: |
        echo "## Security Scan Results for ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
        if [ "${{ steps.upload-sarif.outcome }}" == "success" ]; then
          echo "✅ SARIF results uploaded to GitHub Security tab" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ SARIF upload to Security tab failed - results saved as artifact" >> $GITHUB_STEP_SUMMARY
          echo "💡 This may indicate GitHub Advanced Security is not enabled for this repository" >> $GITHUB_STEP_SUMMARY
        fi

        # Extract and display summary from SARIF file
        if command -v jq >/dev/null 2>&1; then
          vulnerabilities=$(jq '.runs[0].results | length' trivy-results-${{ matrix.service }}.sarif 2>/dev/null || echo "0")
          echo "🔍 Total vulnerabilities found: $vulnerabilities" >> $GITHUB_STEP_SUMMARY
        fi
